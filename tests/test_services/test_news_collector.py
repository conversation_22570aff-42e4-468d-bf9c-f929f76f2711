"""
Unit tests for the news collection service.
"""

import pytest
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timezone

from app.services.news_collector import NewsCollectionService, CollectedArticle
from app.core.models import NewsArticle


class TestNewsCollectionService:
    """Test cases for NewsCollectionService."""
    
    def test_init(self, db_session):
        """Test service initialization."""
        with patch('app.services.news_collector.TavilyClient') as mock_tavily, \
             patch('app.services.news_collector.Agent') as mock_agent:
            
            service = NewsCollectionService(db_session)
            
            assert service.db == db_session
            assert service.settings is not None
            mock_tavily.assert_called_once()
            mock_agent.assert_called_once()
    
    def test_init_missing_api_keys(self, db_session):
        """Test initialization with missing API keys."""
        with patch('app.services.news_collector.get_settings') as mock_settings:
            mock_settings.return_value.tavily_api_key = None

            with pytest.raises(ValueError, match="TAVILY_API_KEY is required"):
                NewsCollectionService(db_session)
    
    @patch('app.services.news_collector.TavilyClient')
    @patch('app.services.news_collector.Agent')
    def test_collect_from_search_query(self, mock_agent, mock_tavily, db_session):
        """Test collecting articles from search query."""
        # Setup mocks
        mock_tavily_instance = Mock()
        mock_tavily.return_value = mock_tavily_instance
        mock_tavily_instance.search.return_value = {
            "results": [
                {
                    "url": "https://example.com/article1",
                    "title": "Carbon Tax News",
                    "content": "Carbon tax content"
                },
                {
                    "url": "https://example.com/article2",
                    "title": "ETS Update",
                    "content": "ETS content"
                }
            ]
        }
        mock_tavily_instance.extract.return_value = {
            "results": [{"content": "Full article content"}]
        }
        
        service = NewsCollectionService(db_session)
        service.tavily_client = mock_tavily_instance
        
        # Test
        articles = service._collect_from_search_query("carbon regulations")
        
        # Assertions
        assert len(articles) == 2
        assert articles[0].title == "Carbon Tax News"
        assert articles[0].url == "https://example.com/article1"
        assert articles[1].title == "ETS Update"
        assert articles[1].url == "https://example.com/article2"
        
        mock_tavily_instance.search.assert_called_once()
        assert mock_tavily_instance.extract.call_count == 2
    
    @patch('app.services.news_collector.TavilyClient')
    @patch('app.services.news_collector.Agent')
    def test_collect_from_specific_source(self, mock_agent, mock_tavily, db_session):
        """Test collecting articles from specific source."""
        # Setup mocks
        mock_tavily_instance = Mock()
        mock_tavily.return_value = mock_tavily_instance
        mock_tavily_instance.extract.side_effect = [
            # First call for main page
            {
                "results": [{"content": "Page content with article links"}]
            },
            # Subsequent calls for individual articles
            {
                "results": [{"content": "Article 1 content"}]
            },
            {
                "results": [{"content": "Article 2 content"}]
            }
        ]
        
        mock_agent_instance = Mock()
        mock_agent.return_value = mock_agent_instance
        mock_result = Mock()
        mock_result.output.urls = [
            "https://example.com/article1",
            "https://example.com/article2"
        ]
        mock_agent_instance.run_sync.return_value = mock_result
        
        service = NewsCollectionService(db_session)
        service.tavily_client = mock_tavily_instance
        service.url_extractor_agent = mock_agent_instance
        
        # Test
        articles = service._collect_from_specific_source("https://example.com/news")
        
        # Assertions
        assert len(articles) == 2
        assert all(isinstance(article, CollectedArticle) for article in articles)
        mock_tavily_instance.extract.assert_called()
        mock_agent_instance.run_sync.assert_called_once()
    
    @patch('app.services.news_collector.TavilyClient')
    @patch('app.services.news_collector.Agent')
    def test_extract_urls_fallback(self, mock_agent, mock_tavily, db_session):
        """Test fallback URL extraction using regex."""
        service = NewsCollectionService(db_session)
        
        content = '''
        <a href="/article/2024/01/15/carbon-tax-news">Carbon Tax</a>
        <a href="/news/climate-policy-update">Climate Policy</a>
        <a href="/sustainability/green-finance">Green Finance</a>
        '''
        
        urls = service._extract_urls_fallback(content, "https://example.com")
        
        assert len(urls) > 0
        assert all(url.startswith("https://example.com") for url in urls)
    
    def test_extract_title_from_content(self, db_session):
        """Test title extraction from content."""
        with patch('app.services.news_collector.TavilyClient'), \
             patch('app.services.news_collector.Agent'):
            
            service = NewsCollectionService(db_session)
            
            content = """
            Menu | Search | Login
            
            Breaking: New Carbon Tax Legislation Passes
            
            The Senate voted today to pass comprehensive carbon tax legislation...
            """
            
            title = service._extract_title_from_content(content)
            assert title == "Breaking: New Carbon Tax Legislation Passes"
    
    def test_extract_source_name(self, db_session):
        """Test source name extraction from URL."""
        with patch('app.services.news_collector.TavilyClient'), \
             patch('app.services.news_collector.Agent'):
            
            service = NewsCollectionService(db_session)
            
            # Test various URL formats
            assert service._extract_source_name("https://www.reuters.com/article") == "Reuters"
            assert service._extract_source_name("https://bloomberg.com/news") == "Bloomberg"
            assert service._extract_source_name("https://example.com/path") == "Example"
    
    def test_is_valid_article_url(self, db_session):
        """Test URL validation."""
        with patch('app.services.news_collector.TavilyClient'), \
             patch('app.services.news_collector.Agent'):
            
            service = NewsCollectionService(db_session)
            
            # Valid URLs
            assert service._is_valid_article_url("https://example.com/article")
            assert service._is_valid_article_url("http://news.com/story")
            
            # Invalid URLs
            assert not service._is_valid_article_url("javascript:void(0)")
            assert not service._is_valid_article_url("mailto:<EMAIL>")
            assert not service._is_valid_article_url("#anchor")
    
    def test_save_articles_to_database(self, db_session):
        """Test saving collected articles to database."""
        with patch('app.services.news_collector.TavilyClient'), \
             patch('app.services.news_collector.Agent'):
            
            service = NewsCollectionService(db_session)
            
            # Create test articles
            articles = [
                CollectedArticle(
                    title="Test Article 1",
                    url="https://example.com/article1",
                    content="Test content 1",
                    source_name="Test Source"
                ),
                CollectedArticle(
                    title="Test Article 2", 
                    url="https://example.com/article2",
                    content="Test content 2",
                    source_name="Test Source"
                )
            ]
            
            # Save articles
            saved_articles = service.save_articles_to_database(articles)
            
            # Assertions
            assert len(saved_articles) == 2
            assert all(isinstance(article, NewsArticle) for article in saved_articles)
            assert saved_articles[0].title == "Test Article 1"
            assert saved_articles[1].title == "Test Article 2"
            
            # Verify in database
            db_articles = db_session.query(NewsArticle).all()
            assert len(db_articles) == 2
    
    def test_save_duplicate_articles(self, db_session, sample_news_article):
        """Test handling of duplicate articles."""
        with patch('app.services.news_collector.TavilyClient'), \
             patch('app.services.news_collector.Agent'):
            
            service = NewsCollectionService(db_session)
            
            # Try to save article with same URL
            duplicate_article = CollectedArticle(
                title="Different Title",
                url=sample_news_article.url,  # Same URL as existing article
                content="Different content",
                source_name="Different Source"
            )
            
            saved_articles = service.save_articles_to_database([duplicate_article])
            
            # Should not save duplicate
            assert len(saved_articles) == 0
            
            # Verify only original article exists
            db_articles = db_session.query(NewsArticle).all()
            assert len(db_articles) == 1
            assert db_articles[0].id == sample_news_article.id
    
    @patch('app.services.news_collector.TavilyClient')
    @patch('app.services.news_collector.Agent')
    def test_collect_and_save_news(self, mock_agent, mock_tavily, db_session):
        """Test complete collect and save workflow."""
        # Setup mocks
        mock_tavily_instance = Mock()
        mock_tavily.return_value = mock_tavily_instance
        mock_tavily_instance.search.return_value = {
            "results": [
                {
                    "url": "https://example.com/article1",
                    "title": "Test Article",
                    "content": "Test content"
                }
            ]
        }
        mock_tavily_instance.extract.return_value = {
            "results": [{"content": "Full content"}]
        }
        
        service = NewsCollectionService(db_session)
        service.tavily_client = mock_tavily_instance
        
        # Test
        summary = service.collect_and_save_news()
        
        # Assertions
        assert "collected_count" in summary
        assert "saved_count" in summary
        assert "duplicate_count" in summary
        assert "sources" in summary
        assert "timestamp" in summary
        assert summary["collected_count"] >= 0
        assert summary["saved_count"] >= 0
    
    @patch('app.services.news_collector.TavilyClient')
    @patch('app.services.news_collector.Agent')
    def test_error_handling_in_search(self, mock_agent, mock_tavily, db_session):
        """Test error handling during search operations."""
        # Setup mock to raise exception
        mock_tavily_instance = Mock()
        mock_tavily.return_value = mock_tavily_instance
        mock_tavily_instance.search.side_effect = Exception("API Error")
        
        service = NewsCollectionService(db_session)
        service.tavily_client = mock_tavily_instance
        
        # Test - should not raise exception, should return empty list
        articles = service._collect_from_search_query("test query")
        assert articles == []
    
    @patch('app.services.news_collector.TavilyClient')
    @patch('app.services.news_collector.Agent')
    def test_ai_agent_fallback(self, mock_agent, mock_tavily, db_session):
        """Test fallback when AI agent fails."""
        # Setup mocks
        mock_tavily_instance = Mock()
        mock_tavily.return_value = mock_tavily_instance
        mock_tavily_instance.extract.return_value = {
            "results": [{"content": "Page content"}]
        }
        
        mock_agent_instance = Mock()
        mock_agent.return_value = mock_agent_instance
        mock_agent_instance.run_sync.side_effect = Exception("AI Error")
        
        service = NewsCollectionService(db_session)
        service.tavily_client = mock_tavily_instance
        service.url_extractor_agent = mock_agent_instance
        
        # Test - should use fallback regex extraction
        with patch.object(service, '_extract_urls_fallback', return_value=["https://example.com/article"]) as mock_fallback:
            articles = service._collect_from_specific_source("https://example.com/news")
            mock_fallback.assert_called_once()
    
    def test_service_cleanup(self, db_session):
        """Test service cleanup on deletion."""
        with patch('app.services.news_collector.TavilyClient'), \
             patch('app.services.news_collector.Agent'):
            
            service = NewsCollectionService(db_session)
            
            # Mock the database session close method
            service.db.close = Mock()
            
            # Delete service
            del service
            
            # Note: __del__ is not guaranteed to be called immediately,
            # so we can't reliably test it this way. This is more of a
            # documentation of the intended behavior.
