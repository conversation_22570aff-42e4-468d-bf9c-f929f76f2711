"""
Unit tests for the AI news parsing service.
"""

import pytest
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timezone

from app.services.ai_parser import AINewsParsingService, ModelManager
from app.core.models import NewsArticle
from tests.conftest import TEST_AI_CLASSIFICATION, TEST_AI_SUMMARY, TEST_AI_KEY_POINTS


class TestModelManager:
    """Test cases for ModelManager singleton."""
    
    def test_singleton_behavior(self):
        """Test that ModelManager returns the same instance."""
        with patch('app.services.ai_parser.OpenAIChatModel') as mock_model:
            mock_instance = Mock()
            mock_model.return_value = mock_instance
            
            # Reset singleton
            ModelManager._instance = None
            
            # Get instances
            instance1 = ModelManager.get_model()
            instance2 = ModelManager.get_model()
            
            # Should be the same instance
            assert instance1 is instance2
            assert instance1 is mock_instance
            
            # Model should only be created once
            mock_model.assert_called_once()
    
    def test_missing_api_key(self):
        """Test error when API key is missing."""
        with patch('app.services.ai_parser.get_settings') as mock_settings:
            mock_settings.return_value.openrouter_api_key = None

            # Reset singleton
            ModelManager._instance = None

            with pytest.raises(RuntimeError, match="Missing OPENROUTER_API_KEY"):
                ModelManager.get_model()


class TestAINewsParsingService:
    """Test cases for AINewsParsingService."""
    
    def test_init(self, db_session):
        """Test service initialization."""
        with patch('app.services.ai_parser.ModelManager.get_model') as mock_get_model:
            mock_model = Mock()
            mock_get_model.return_value = mock_model
            
            service = AINewsParsingService(db_session)
            
            assert service.db == db_session
            assert service.model == mock_model
            assert service._agents == {}
    
    def test_get_classification_agent(self, db_session):
        """Test classification agent creation and caching."""
        with patch('app.services.ai_parser.ModelManager.get_model') as mock_get_model, \
             patch('app.services.ai_parser.Agent') as mock_agent:
            
            mock_model = Mock()
            mock_get_model.return_value = mock_model
            mock_agent_instance = Mock()
            mock_agent.return_value = mock_agent_instance
            
            service = AINewsParsingService(db_session)
            
            # First call should create agent
            agent1 = service._get_classification_agent()
            assert agent1 == mock_agent_instance
            mock_agent.assert_called_once()
            
            # Second call should return cached agent
            agent2 = service._get_classification_agent()
            assert agent2 == mock_agent_instance
            assert agent1 is agent2
            # Agent constructor should still only be called once
            assert mock_agent.call_count == 1
    
    def test_get_content_agent(self, db_session):
        """Test content agent creation and caching."""
        with patch('app.services.ai_parser.ModelManager.get_model') as mock_get_model, \
             patch('app.services.ai_parser.Agent') as mock_agent:
            
            mock_model = Mock()
            mock_get_model.return_value = mock_model
            mock_agent_instance = Mock()
            mock_agent.return_value = mock_agent_instance
            
            service = AINewsParsingService(db_session)
            
            # Test agent creation and caching
            agent1 = service._get_content_agent()
            agent2 = service._get_content_agent()
            
            assert agent1 == mock_agent_instance
            assert agent1 is agent2
            mock_agent.assert_called_once()
    
    def test_extract_classification_success(self, db_session, sample_news_article):
        """Test successful classification extraction."""
        with patch('app.services.ai_parser.ModelManager.get_model') as mock_get_model, \
             patch('app.services.ai_parser.Agent') as mock_agent:
            
            # Setup mocks
            mock_model = Mock()
            mock_get_model.return_value = mock_model
            
            mock_agent_instance = Mock()
            mock_agent.return_value = mock_agent_instance
            
            mock_result = Mock()
            import json
            mock_result.output = json.dumps(TEST_AI_CLASSIFICATION)
            mock_agent_instance.run_sync.return_value = mock_result
            
            service = AINewsParsingService(db_session)
            service._agents['classification'] = mock_agent_instance
            
            # Test
            from app.services.ai_parser import ExtractInput
            extract_input = ExtractInput(
                url=sample_news_article.url,
                outlet=sample_news_article.source_name,
                raw_content=sample_news_article.content
            )
            
            result = service._extract_classification(extract_input)
            
            # Assertions
            assert result == TEST_AI_CLASSIFICATION
            mock_agent_instance.run_sync.assert_called_once()
    
    def test_extract_classification_failure(self, db_session, sample_news_article):
        """Test classification extraction with AI failure."""
        with patch('app.services.ai_parser.ModelManager.get_model') as mock_get_model, \
             patch('app.services.ai_parser.Agent') as mock_agent:
            
            # Setup mocks
            mock_model = Mock()
            mock_get_model.return_value = mock_model
            
            mock_agent_instance = Mock()
            mock_agent.return_value = mock_agent_instance
            mock_agent_instance.run_sync.side_effect = Exception("AI Error")
            
            service = AINewsParsingService(db_session)
            service._agents['classification'] = mock_agent_instance
            
            # Test
            from app.services.ai_parser import ExtractInput
            extract_input = ExtractInput(
                url=sample_news_article.url,
                outlet=sample_news_article.source_name,
                raw_content=sample_news_article.content
            )
            
            result = service._extract_classification(extract_input)
            
            # Should return default values on failure
            assert result["category"] == "Unknown"
            assert result["type"] == "Unknown"
            assert result["jurisdictions"] == []
            assert result["sectors"] == []
    
    def test_extract_content_success(self, db_session, sample_news_article):
        """Test successful content extraction."""
        with patch('app.services.ai_parser.ModelManager.get_model') as mock_get_model, \
             patch('app.services.ai_parser.Agent') as mock_agent:
            
            # Setup mocks
            mock_model = Mock()
            mock_get_model.return_value = mock_model
            
            mock_agent_instance = Mock()
            mock_agent.return_value = mock_agent_instance
            
            mock_result = Mock()
            import json
            mock_result.output = json.dumps({
                "title": "Extracted Title",
                "summary": TEST_AI_SUMMARY,
                "key_points": TEST_AI_KEY_POINTS,
                "original_text": "Cleaned original text"
            })
            mock_agent_instance.run_sync.return_value = mock_result
            
            service = AINewsParsingService(db_session)
            service._agents['content'] = mock_agent_instance
            
            # Test
            from app.services.ai_parser import ExtractInput
            extract_input = ExtractInput(
                url=sample_news_article.url,
                outlet=sample_news_article.source_name,
                raw_content=sample_news_article.content
            )
            
            result = service._extract_content(extract_input)
            
            # Assertions
            assert result["title"] == "Extracted Title"
            assert result["summary"] == TEST_AI_SUMMARY
            assert result["key_points"] == TEST_AI_KEY_POINTS
            mock_agent_instance.run_sync.assert_called_once()
    
    def test_parse_article_success(self, db_session, sample_news_article):
        """Test successful article parsing."""
        with patch('app.services.ai_parser.ModelManager.get_model') as mock_get_model:
            mock_model = Mock()
            mock_get_model.return_value = mock_model
            
            service = AINewsParsingService(db_session)
            
            # Mock the extraction methods
            service._extract_classification = Mock(return_value=TEST_AI_CLASSIFICATION)
            service._extract_content = Mock(return_value={
                "title": "Parsed Title",
                "summary": TEST_AI_SUMMARY,
                "key_points": TEST_AI_KEY_POINTS,
                "original_text": "Cleaned text"
            })
            
            # Test
            result = service.parse_article(sample_news_article)
            
            # Assertions
            assert "classification" in result
            assert "content" in result
            assert "parsed_at" in result
            assert result["classification"] == TEST_AI_CLASSIFICATION
            assert result["content"]["summary"] == TEST_AI_SUMMARY
            
            service._extract_classification.assert_called_once()
            service._extract_content.assert_called_once()
    
    def test_update_article_with_ai_data(self, db_session, sample_news_article):
        """Test updating article with AI parsing results."""
        with patch('app.services.ai_parser.ModelManager.get_model') as mock_get_model:
            mock_model = Mock()
            mock_get_model.return_value = mock_model
            
            service = AINewsParsingService(db_session)
            
            # Prepare parsing result
            parsing_result = {
                "classification": TEST_AI_CLASSIFICATION,
                "content": {
                    "title": "Parsed Title",
                    "summary": TEST_AI_SUMMARY,
                    "key_points": TEST_AI_KEY_POINTS,
                    "original_text": "Cleaned text"
                }
            }
            
            # Test
            updated_article = service.update_article_with_ai_data(sample_news_article, parsing_result)
            
            # Assertions
            assert updated_article.is_processed == True
            assert updated_article.processed_at is not None
            assert updated_article.ai_classification == TEST_AI_CLASSIFICATION
            assert updated_article.ai_summary == TEST_AI_SUMMARY
            assert updated_article.ai_key_points == TEST_AI_KEY_POINTS
            
            # Verify in database
            db_session.refresh(sample_news_article)
            assert sample_news_article.is_processed == True
    
    def test_process_unprocessed_articles(self, db_session):
        """Test processing all unprocessed articles."""
        with patch('app.services.ai_parser.ModelManager.get_model') as mock_get_model:
            mock_model = Mock()
            mock_get_model.return_value = mock_model
            
            # Create unprocessed articles
            article1 = NewsArticle(
                title="Article 1",
                url="https://example.com/article1",
                content="Content 1",
                source_name="Source 1",
                collected_at=datetime.now(timezone.utc),
                is_processed=False
            )
            article2 = NewsArticle(
                title="Article 2",
                url="https://example.com/article2",
                content="Content 2",
                source_name="Source 2",
                collected_at=datetime.now(timezone.utc),
                is_processed=False
            )
            
            db_session.add_all([article1, article2])
            db_session.commit()
            
            service = AINewsParsingService(db_session)
            
            # Mock the parse_article method
            service.parse_article = Mock(return_value={
                "classification": TEST_AI_CLASSIFICATION,
                "content": {
                    "summary": TEST_AI_SUMMARY,
                    "key_points": TEST_AI_KEY_POINTS
                }
            })
            
            # Test
            summary = service.process_unprocessed_articles()
            
            # Assertions
            assert summary["total_unprocessed"] == 2
            assert summary["processed_count"] == 2
            assert summary["failed_count"] == 0
            assert "timestamp" in summary
            
            # Verify articles are marked as processed
            db_session.refresh(article1)
            db_session.refresh(article2)
            assert article1.is_processed == True
            assert article2.is_processed == True
    
    def test_process_unprocessed_articles_with_failures(self, db_session):
        """Test processing with some failures."""
        with patch('app.services.ai_parser.ModelManager.get_model') as mock_get_model:
            mock_model = Mock()
            mock_get_model.return_value = mock_model
            
            # Create unprocessed articles
            article1 = NewsArticle(
                title="Article 1",
                url="https://example.com/article1",
                content="Content 1",
                source_name="Source 1",
                collected_at=datetime.now(timezone.utc),
                is_processed=False
            )
            article2 = NewsArticle(
                title="Article 2",
                url="https://example.com/article2",
                content="Content 2",
                source_name="Source 2",
                collected_at=datetime.now(timezone.utc),
                is_processed=False
            )
            
            db_session.add_all([article1, article2])
            db_session.commit()
            
            service = AINewsParsingService(db_session)
            
            # Mock parse_article to fail for second article
            def mock_parse_article(article):
                if article.id == article2.id:
                    raise Exception("Parsing failed")
                return {
                    "classification": TEST_AI_CLASSIFICATION,
                    "content": {"summary": TEST_AI_SUMMARY, "key_points": TEST_AI_KEY_POINTS}
                }
            
            service.parse_article = Mock(side_effect=mock_parse_article)
            
            # Test
            summary = service.process_unprocessed_articles()
            
            # Assertions
            assert summary["total_unprocessed"] == 2
            assert summary["processed_count"] == 1
            assert summary["failed_count"] == 1
    
    def test_generate_daily_summary_empty(self, db_session):
        """Test daily summary generation with no articles."""
        with patch('app.services.ai_parser.ModelManager.get_model') as mock_get_model:
            mock_model = Mock()
            mock_get_model.return_value = mock_model
            
            service = AINewsParsingService(db_session)
            
            # Test with empty list
            summary = service.generate_daily_summary([])
            
            # Assertions
            assert summary["summary"] == "No articles processed today."
            assert summary["key_developments"] == []
            assert summary["statistics"]["total_articles"] == 0
            assert "generated_at" in summary
    
    def test_generate_daily_summary_with_articles(self, db_session, processed_news_article):
        """Test daily summary generation with processed articles."""
        with patch('app.services.ai_parser.ModelManager.get_model') as mock_get_model, \
             patch('app.services.ai_parser.Agent') as mock_agent:
            
            mock_model = Mock()
            mock_get_model.return_value = mock_model
            
            mock_agent_instance = Mock()
            mock_agent.return_value = mock_agent_instance
            
            # Mock AI summary response
            mock_result = Mock()
            import json
            mock_result.output = json.dumps({
                "executive_summary": "Daily summary of carbon regulation news",
                "key_developments": ["Development 1", "Development 2"],
                "regulatory_changes": ["Change 1"],
                "market_implications": ["Implication 1"],
                "important_dates": ["Date 1"]
            })
            mock_agent_instance.run_sync.return_value = mock_result
            
            service = AINewsParsingService(db_session)
            service._agents['summary'] = mock_agent_instance
            
            # Test
            summary = service.generate_daily_summary([processed_news_article])
            
            # Assertions
            assert summary["executive_summary"] == "Daily summary of carbon regulation news"
            assert len(summary["key_developments"]) == 2
            assert summary["statistics"]["total_articles"] == 1
            assert summary["statistics"]["processed_articles"] == 1
            assert "categories" in summary["statistics"]
            assert "generated_at" in summary
    
    def test_generate_daily_summary_ai_failure(self, db_session, processed_news_article):
        """Test daily summary generation when AI fails."""
        with patch('app.services.ai_parser.ModelManager.get_model') as mock_get_model, \
             patch('app.services.ai_parser.Agent') as mock_agent:
            
            mock_model = Mock()
            mock_get_model.return_value = mock_model
            
            mock_agent_instance = Mock()
            mock_agent.return_value = mock_agent_instance
            mock_agent_instance.run_sync.side_effect = Exception("AI Error")
            
            service = AINewsParsingService(db_session)
            service._agents['summary'] = mock_agent_instance
            
            # Test
            summary = service.generate_daily_summary([processed_news_article])
            
            # Should still generate summary with statistics
            assert summary["statistics"]["total_articles"] == 1
            assert summary["executive_summary"] == "Failed to generate AI summary"
            assert "generated_at" in summary
    
    def test_service_cleanup(self, db_session):
        """Test service cleanup on deletion."""
        with patch('app.services.ai_parser.ModelManager.get_model') as mock_get_model:
            mock_model = Mock()
            mock_get_model.return_value = mock_model
            
            service = AINewsParsingService(db_session)
            
            # Mock the database session close method
            service.db.close = Mock()
            
            # Delete service
            del service
            
            # Note: __del__ is not guaranteed to be called immediately
