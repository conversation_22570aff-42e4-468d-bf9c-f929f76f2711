# =============================================================================
# ENVIRONMENT SETTINGS
# =============================================================================
environment: development
debug: true
log_level: INFO

# =============================================================================
# API KEYS - CONFIGURED VIA ENVIRONMENT VARIABLES ONLY
# =============================================================================
# API keys are configured via environment variables for security:
# OPENROUTER_API_KEY=your-openrouter-key-here
# TAVILY_API_KEY=your-tavily-key-here
#
# These should be set in your .env file and NOT in this YAML file
# for security reasons (YAML files are typically committed to version control)
database:
  url: sqlite:///./carbon_news.db
  echo: false
api:
  host: 0.0.0.0
  port: 8000
  debug: false
news_collector:
  max_articles_per_source: 10
  default_time_range: day
  search_queries:
  - carbon regulations emission standards environmental policy
  # - clean energy regulations carbon policy sustainability
  # - carbon accounting standards disclosure reporting
  # - carbon pricing markets ETS carbon tax
  # - climate regulations environmental compliance
  # - carbon border adjustment mechanism CBAM
  # - scope 3 emissions reporting requirements
  # - net zero commitments corporate sustainability
  # - carbon offset verification standards
  # - renewable energy mandates policy
  specific_sources:
  - https://www.reuters.com/sustainability/clean-energy/
  # - https://www.reuters.com/sustainability/climate-energy/
  # - https://www.bloomberg.com/green
  # - https://www.carbonbrief.org/
  # - https://www.climatechangenews.com/
  rss_feeds:
  - https://www.carbonbrief.org/feed/
  - https://feeds.feedburner.com/EnvironmentalDefenseFund
  # - https://www.epa.gov/newsroom/search/rss
  # - https://www.iea.org/news/rss
  search_settings:
    include_domains: []
    exclude_domains:
    - twitter.com
    - facebook.com
    - instagram.com
    - tiktok.com
    search_depth: basic
    extract_full_content: true
    min_content_length: 100
scheduler:
  daily_run_time: 09:00
  max_task_history: 100
  task_timeout_minutes: 30
  enable_auto_scheduling: true
  retry_settings:
    max_retries: 3
    retry_delay_minutes: 5
notifications:
  enable_notifications: true
  webhook_url: null
  slack_webhook_url: null
  email:
    enabled: false
    smtp_server: null
    smtp_port: 587
    username: null
    password: null
    from_address: null
    to_addresses: []
  triggers:
    on_collection_complete: true
    on_error: true
    on_high_priority_articles: false
    min_articles_threshold: 5
ai_processing:
  # Model settings for different AI services
  models:
    # Model for news parsing and content analysis
    news_parser:
      model_name: "openai/gpt-4.1-mini"
      temperature: 0.1
      max_tokens: 2000

    # Model for URL extraction from web pages
    url_extractor:
      model_name: "openai/gpt-4.1-mini"
      temperature: 0.0
      max_tokens: 1000

    # Model for content classification
    classifier:
      model_name: "openai/gpt-4.1-mini"
      temperature: 0.1
      max_tokens: 1500

    # Model for summary generation
    summarizer:
      model_name: "openai/gpt-4.1-mini"
      temperature: 0.2
      max_tokens: 3000

  analysis_settings:
    enable_sentiment_analysis: true
    enable_topic_classification: true
    enable_summary_generation: true
    min_confidence_score: 0.7
content_filtering:
  required_keywords:
  - carbon
  - emission
  - climate
  - sustainability
  - environmental
  - green
  - renewable
  priority_keywords:
  - regulation
  - policy
  - law
  - mandate
  - requirement
  - compliance
  - standard
  exclude_keywords:
  - sports
  - entertainment
  - celebrity
  languages:
  - en
  - es
  quality_filters:
    min_word_count: 50
    max_word_count: 10000
    require_publication_date: true
monitoring:
  health_check:
    enabled: true
    check_interval_minutes: 5
  performance:
    enabled: true
    retention_days: 30
  alerts:
    max_collection_time_minutes: 60
    max_error_rate_percent: 10
    max_days_without_articles: 2

# =============================================================================
# AI PROMPTS CONFIGURATION
# =============================================================================
prompts:
  # AI Parser Service Prompts
  ai_parser:
    # System prompts for different AI agents
    classification_system_prompt: |
      You are an expert at classifying carbon regulation and climate policy news.

      Analyze the article and extract:
      - category: Choose from: Carbon pricing & markets, Disclosure & reporting, Sector standards, Energy transition, Transport, Buildings, Agriculture & land-use, Offsets & removals, Finance & ESG, Litigation & enforcement, International & trade, Other, Unknown
      - type: Choose from: Regulatory Update (Final), Proposed Rule, Legislation, Court/Enforcement, Guidance/Standard, Market/Auction, Corporate Disclosure, Funding/Incentive, Event, Research/Report, Other, Unknown
      - jurisdictions: List of countries/regions (ISO codes or names)
      - sectors: List of affected sectors (e.g., Power, Oil & Gas, Manufacturing, Transport, Buildings, Agriculture, Finance)

      Return classification as JSON only, without markdown formatting.

    content_extraction_system_prompt: |
      You are an expert at extracting and summarizing news content.

      Extract from the article:
      - title: Clean, concise title
      - summary: 2-4 sentence objective summary
      - key_points: 3-7 bullet points with key facts, numbers, and actions
      - original_text: Cleaned article text (remove ads, menus, boilerplate)

      Focus on factual information, dates, numbers, and concrete actions.
      Return a JSON object with these fields.

    summary_generation_system_prompt: |
      You are an expert at creating executive summaries of carbon regulation and climate policy news.

      Create a comprehensive daily summary that includes:
      - Executive summary paragraph
      - Key developments by category
      - Important dates and deadlines
      - Regulatory changes and their implications
      - Market impacts and trends

      Focus on actionable insights for climate experts and policy professionals.

    # Prompt templates for specific operations
    classification_prompt_template: |
      Classify this carbon regulation/climate policy article:

      URL: {url}
      Source: {source}

      Article content:
      {content}

      Return classification as JSON only, without markdown formatting.

    content_extraction_prompt_template: |
      Extract and normalize content from this article:

      URL: {url}
      Source: {source}

      Article content:
      {content}

      Return content as JSON only, without markdown formatting.

    daily_summary_prompt_template: |
      Create a comprehensive daily summary of carbon regulation and climate policy news based on these {article_count} articles:

      {article_summaries}

      Include:
      1. Executive summary (2-3 paragraphs)
      2. Key developments by category
      3. Important regulatory changes
      4. Market implications
      5. Upcoming deadlines or dates

      Return as JSON with fields: executive_summary, key_developments, regulatory_changes, market_implications, important_dates

  # News Collector Service Prompts
  news_collector:
    # System prompt for URL extraction agent
    url_extractor_system_prompt: |
      You are an expert at extracting article URLs from web page content.

      Your task is to:
      1. Identify links that point to news articles (not navigation, ads, or other pages)
      2. Filter for articles that appear to be recent and relevant to carbon regulation, climate policy, or environmental news
      3. Return only the URLs that look like individual news articles

      Look for patterns like:
      - URLs containing dates or article IDs
      - Links with article-like titles related to carbon, climate, energy, or environmental policy
      - Content that appears to be news articles rather than category pages

      Return only the URLs in the specified format.

    # Prompt template for AI URL extraction
    url_extraction_prompt_template: |
      Extract article URLs from this web page content that appear to be news articles about carbon regulation, climate policy, or environmental news.

      Base URL: {base_url}

      Page content:
      {page_content}

      Look for links that:
      1. Point to individual news articles
      2. Appear to be recent and relevant to carbon/climate/environmental topics
      3. Are not navigation links, category pages, or advertisements

      Return the complete URLs. If URLs are relative, they should be resolved against the base URL.
