#!/usr/bin/env python3
"""
Test script to demonstrate RSS feed functionality in the news collector.
This script shows how the RSS feed parsing works with real feeds.
"""

import os
import sys
from pathlib import Path

# Add the app directory to the Python path
sys.path.insert(0, str(Path(__file__).parent / "app"))

from app.core.config import get_settings
from app.services.news_collector import NewsCollectionService
from app.core.database import get_db_session


def test_rss_feed_parsing():
    """Test RSS feed parsing with a simple example."""
    print("🔍 Testing RSS Feed Functionality")
    print("=" * 50)
    
    # Check if required environment variables are set
    settings = get_settings()
    if not settings.tavily_api_key:
        print("❌ TAVILY_API_KEY not set. Please set it in your .env file.")
        return
    
    if not settings.openrouter_api_key:
        print("❌ OPENROUTER_API_KEY not set. Please set it in your .env file.")
        return
    
    print("✅ API keys configured")
    
    # Get database session
    db_session = get_db_session()
    
    try:
        # Initialize the news collection service
        print("\n📡 Initializing News Collection Service...")
        service = NewsCollectionService(db_session)
        
        # Show configured RSS feeds
        print(f"\n📰 Configured RSS feeds:")
        for i, feed_url in enumerate(service.settings.news_collector.rss_feeds, 1):
            print(f"  {i}. {feed_url}")
        
        # Test RSS feed parsing with the first configured feed
        if service.settings.news_collector.rss_feeds:
            test_feed_url = service.settings.news_collector.rss_feeds[0]
            print(f"\n🔄 Testing RSS feed parsing with: {test_feed_url}")
            
            # Collect articles from the RSS feed
            articles = service._collect_from_rss_feed(test_feed_url)
            
            print(f"\n📊 Results:")
            print(f"  • Articles collected: {len(articles)}")
            
            if articles:
                print(f"\n📄 Sample articles:")
                for i, article in enumerate(articles[:3], 1):  # Show first 3 articles
                    print(f"\n  {i}. {article.title}")
                    print(f"     URL: {article.url}")
                    print(f"     Source: {article.source_name}")
                    print(f"     Published: {article.published_date}")
                    print(f"     Content length: {len(article.content)} characters")
            else:
                print("  No articles were collected from the RSS feed.")
        
        else:
            print("❌ No RSS feeds configured in settings.")
        
        print(f"\n✅ RSS feed functionality test completed!")
        
    except Exception as e:
        print(f"\n❌ Error during RSS feed testing: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        db_session.close()


def show_configuration():
    """Show the current RSS feed configuration."""
    print("\n🔧 Current RSS Feed Configuration")
    print("=" * 50)
    
    settings = get_settings()
    
    print(f"RSS feeds configured: {len(settings.news_collector.rss_feeds)}")
    for i, feed_url in enumerate(settings.news_collector.rss_feeds, 1):
        print(f"  {i}. {feed_url}")
    
    print(f"\nMax articles per source: {settings.news_collector.max_articles_per_source}")
    print(f"Default time range: {settings.news_collector.default_time_range}")


if __name__ == "__main__":
    print("🚀 RSS Feed Functionality Test")
    print("=" * 50)
    
    show_configuration()
    
    # Ask user if they want to run the test
    response = input("\nDo you want to test RSS feed parsing? (y/n): ").lower().strip()
    
    if response in ['y', 'yes']:
        test_rss_feed_parsing()
    else:
        print("Test skipped.")
    
    print("\n🎉 Done!")
