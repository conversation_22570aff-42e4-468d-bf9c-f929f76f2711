"""
AI-powered news parsing service for extracting structured information from news articles.
"""

import re
from datetime import datetime, timezone
from typing import Optional, Dict, Any, List
from dataclasses import dataclass
from sqlalchemy.orm import Session

from pydantic_ai import Agent
from pydantic_ai.models.openai import OpenAIChatModel
from pydantic_ai.providers.openrouter import OpenRouterProvider

from ..core.config import get_settings
from ..core.logging import LoggerMixin
from ..core.models import NewsArticle
from ..core.database import get_db_session


# Import the models from playground (we'll need to adapt these)
@dataclass
class ExtractInput:
    """Input data for AI extraction."""
    url: str
    outlet: Optional[str]
    raw_content: str


class Classification:
    """Simplified classification model."""
    def __init__(self, category: str, type: str, jurisdictions: List[str], sectors: Optional[List[str]] = None):
        self.category = category
        self.type = type
        self.jurisdictions = jurisdictions
        self.sectors = sectors or []


class Content:
    """Simplified content model."""
    def __init__(self, title: str, summary: str, key_points: List[str], original_text: str):
        self.title = title
        self.summary = summary
        self.key_points = key_points
        self.original_text = original_text


class ModelManager:
    """Singleton model manager to avoid creating multiple model instances."""
    
    _instance: Optional[OpenAIChatModel] = None
    
    @classmethod
    def get_model(cls) -> OpenAIChatModel:
        if cls._instance is None:
            settings = get_settings()
            api_key = settings.openrouter_api_key
            if not api_key:
                raise RuntimeError("Missing OPENROUTER_API_KEY in environment")
            
            cls._instance = OpenAIChatModel(
                "openai/gpt-4o-mini",
                provider=OpenRouterProvider(api_key=api_key),
            )
        return cls._instance


class AINewsParsingService(LoggerMixin):
    """Service for AI-powered parsing and analysis of news articles."""
    
    def __init__(self, db_session: Optional[Session] = None):
        """Initialize the AI parsing service."""
        self.settings = get_settings()
        self.db = db_session or get_db_session()
        self.model = ModelManager.get_model()
        self._agents = {}
        
        self.log_method_call("__init__")
    
    def _get_classification_agent(self) -> Agent:
        """Get or create classification agent."""
        if 'classification' not in self._agents:
            system_prompt = """You are an expert at classifying carbon regulation and climate policy news.
            
            Analyze the article and extract:
            - category: Choose from: Carbon pricing & markets, Disclosure & reporting, Sector standards, Energy transition, Transport, Buildings, Agriculture & land-use, Offsets & removals, Finance & ESG, Litigation & enforcement, International & trade, Other, Unknown
            - type: Choose from: Regulatory Update (Final), Proposed Rule, Legislation, Court/Enforcement, Guidance/Standard, Market/Auction, Corporate Disclosure, Funding/Incentive, Event, Research/Report, Other, Unknown
            - jurisdictions: List of countries/regions (ISO codes or names)
            - sectors: Optional list of sectors (power, transport, industry, etc.)
            
            Return a JSON object with these fields. Be precise and conservative in your classifications."""
            
            self._agents['classification'] = Agent(
                self.model,
                system_prompt=system_prompt,
            )
        
        return self._agents['classification']
    
    def _get_content_agent(self) -> Agent:
        """Get or create content extraction agent."""
        if 'content' not in self._agents:
            system_prompt = """You are an expert at extracting and summarizing news content.
            
            Extract from the article:
            - title: Clean, concise title
            - summary: 2-4 sentence objective summary
            - key_points: 3-7 bullet points with key facts, numbers, and actions
            - original_text: Cleaned article text (remove ads, menus, boilerplate)
            
            Focus on factual information, dates, numbers, and concrete actions.
            Return a JSON object with these fields."""
            
            self._agents['content'] = Agent(
                self.model,
                system_prompt=system_prompt,
            )
        
        return self._agents['content']
    
    def _get_summary_agent(self) -> Agent:
        """Get or create summary generation agent."""
        if 'summary' not in self._agents:
            system_prompt = """You are an expert at creating executive summaries of carbon regulation and climate policy news.
            
            Create a comprehensive daily summary that includes:
            - Executive summary paragraph
            - Key developments by category
            - Important dates and deadlines
            - Regulatory changes and their implications
            - Market impacts and trends
            
            Focus on actionable insights for climate experts and policy professionals."""
            
            self._agents['summary'] = Agent(
                self.model,
                system_prompt=system_prompt,
            )
        
        return self._agents['summary']

    def _clean_json_response(self, response_text: str) -> str:
        """Clean AI response text to extract JSON from markdown code blocks."""
        # Remove markdown code blocks
        if response_text.strip().startswith('```'):
            # Find the start and end of the code block
            lines = response_text.strip().split('\n')
            start_idx = 0
            end_idx = len(lines)

            # Find start (skip ```json or ```)
            for i, line in enumerate(lines):
                if line.strip().startswith('```'):
                    start_idx = i + 1
                    break

            # Find end (look for closing ```)
            for i in range(len(lines) - 1, -1, -1):
                if lines[i].strip() == '```':
                    end_idx = i
                    break

            # Extract content between code blocks
            json_lines = lines[start_idx:end_idx]
            response_text = '\n'.join(json_lines)

        return response_text.strip()

    def parse_article(self, article: NewsArticle) -> Dict[str, Any]:
        """Parse a single news article using AI."""
        self.log_method_call("parse_article", article_id=article.id)
        
        try:
            extract_input = ExtractInput(
                url=article.url,
                outlet=article.source_name,
                raw_content=article.content
            )
            
            # Extract classification
            classification_result = self._extract_classification(extract_input)
            
            # Extract content
            content_result = self._extract_content(extract_input)
            
            # Prepare results
            parsing_result = {
                "classification": {
                    "category": classification_result.get("category", "Unknown"),
                    "type": classification_result.get("type", "Unknown"),
                    "jurisdictions": classification_result.get("jurisdictions", []),
                    "sectors": classification_result.get("sectors", [])
                },
                "content": {
                    "title": content_result.get("title", article.title),
                    "summary": content_result.get("summary", ""),
                    "key_points": content_result.get("key_points", []),
                    "original_text": content_result.get("original_text", article.content[:8000])
                },
                "parsed_at": datetime.now(timezone.utc).isoformat()
            }
            
            self.logger.info(
                "Article parsed successfully",
                article_id=article.id,
                category=parsing_result["classification"]["category"],
                type=parsing_result["classification"]["type"]
            )
            
            return parsing_result
            
        except Exception as e:
            self.log_error(e, {"article_id": article.id, "article_url": article.url})
            raise
    
    def _extract_classification(self, data: ExtractInput) -> Dict[str, Any]:
        """Extract classification information."""
        prompt = f"""Classify this carbon regulation/climate policy article:

        URL: {data.url}
        Source: {data.outlet or 'Unknown'}

        Article content:
        {data.raw_content[:4000]}

        Return classification as JSON only, without markdown formatting."""

        try:
            result = self._get_classification_agent().run_sync(prompt)
            # Parse the result - handle markdown code blocks
            import json

            output_text = ""
            if hasattr(result, 'output'):
                output_text = result.output if isinstance(result.output, str) else str(result.output)
            else:
                output_text = str(result)

            # Remove markdown code blocks if present
            output_text = self._clean_json_response(output_text)

            return json.loads(output_text)
        except Exception as e:
            self.logger.warning("Classification extraction failed", error=str(e))
            return {
                "category": "Unknown",
                "type": "Unknown",
                "jurisdictions": [],
                "sectors": []
            }
    
    def _extract_content(self, data: ExtractInput) -> Dict[str, Any]:
        """Extract normalized content."""
        prompt = f"""Extract and normalize content from this article:

        URL: {data.url}
        Source: {data.outlet or 'Unknown'}

        Article content:
        {data.raw_content[:6000]}

        Return content as JSON only, without markdown formatting."""

        try:
            result = self._get_content_agent().run_sync(prompt)
            # Parse the result - handle markdown code blocks
            import json

            output_text = ""
            if hasattr(result, 'output'):
                output_text = result.output if isinstance(result.output, str) else str(result.output)
            else:
                output_text = str(result)

            # Remove markdown code blocks if present
            output_text = self._clean_json_response(output_text)

            return json.loads(output_text)
        except Exception as e:
            self.logger.warning("Content extraction failed", error=str(e))
            return {
                "title": "Failed to extract title",
                "summary": "Failed to extract summary",
                "key_points": [],
                "original_text": data.raw_content[:8000]
            }
    
    def update_article_with_ai_data(self, article: NewsArticle, parsing_result: Dict[str, Any]) -> NewsArticle:
        """Update article in database with AI parsing results."""
        self.log_method_call("update_article_with_ai_data", article_id=article.id)
        
        try:
            # Update article with AI results
            article.ai_classification = parsing_result["classification"]
            article.ai_summary = parsing_result["content"]["summary"]
            article.ai_key_points = parsing_result["content"]["key_points"]
            article.ai_details = parsing_result.get("details")
            article.is_processed = True
            article.processed_at = datetime.now(timezone.utc)
            
            self.db.commit()
            self.db.refresh(article)
            
            self.logger.info("Article updated with AI data", article_id=article.id)
            return article
            
        except Exception as e:
            self.db.rollback()
            self.log_error(e, {"article_id": article.id})
            raise
    
    def process_unprocessed_articles(self) -> Dict[str, Any]:
        """Process all unprocessed articles in the database."""
        self.log_method_call("process_unprocessed_articles")
        
        try:
            # Get unprocessed articles
            unprocessed_articles = self.db.query(NewsArticle).filter(
                NewsArticle.is_processed == False
            ).all()
            
            processed_count = 0
            failed_count = 0
            
            for article in unprocessed_articles:
                try:
                    # Parse article
                    parsing_result = self.parse_article(article)
                    
                    # Update article
                    self.update_article_with_ai_data(article, parsing_result)
                    processed_count += 1
                    
                except Exception as e:
                    failed_count += 1
                    self.log_error(e, {"article_id": article.id})
                    continue
            
            summary = {
                "total_unprocessed": len(unprocessed_articles),
                "processed_count": processed_count,
                "failed_count": failed_count,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            
            self.logger.info("Batch processing completed", **summary)
            return summary
            
        except Exception as e:
            self.log_error(e)
            raise
    
    def generate_daily_summary(self, articles: List[NewsArticle]) -> Dict[str, Any]:
        """Generate a daily summary from processed articles."""
        self.log_method_call("generate_daily_summary", article_count=len(articles))
        
        try:
            if not articles:
                return {
                    "summary": "No articles processed today.",
                    "key_developments": [],
                    "statistics": {"total_articles": 0},
                    "generated_at": datetime.now(timezone.utc).isoformat()
                }
            
            # Prepare article summaries for AI
            article_summaries = []
            for article in articles:
                if article.ai_summary and article.ai_classification:
                    article_summaries.append({
                        "title": article.title,
                        "summary": article.ai_summary,
                        "category": article.ai_classification.get("category", "Unknown"),
                        "type": article.ai_classification.get("type", "Unknown"),
                        "jurisdictions": article.ai_classification.get("jurisdictions", []),
                        "key_points": article.ai_key_points or [],
                        "source": article.source_name,
                        "url": article.url
                    })
            
            # Generate summary using AI
            prompt = f"""Create a comprehensive daily summary of carbon regulation and climate policy news based on these {len(article_summaries)} articles:
            
            {str(article_summaries)[:8000]}
            
            Include:
            1. Executive summary (2-3 paragraphs)
            2. Key developments by category
            3. Important regulatory changes
            4. Market implications
            5. Upcoming deadlines or dates
            
            Return as JSON with fields: executive_summary, key_developments, regulatory_changes, market_implications, important_dates"""
            
            try:
                result = self._get_summary_agent().run_sync(prompt)
                import json

                output_text = ""
                if hasattr(result, 'output'):
                    output_text = result.output if isinstance(result.output, str) else str(result.output)
                else:
                    output_text = str(result)

                # Remove markdown code blocks if present
                output_text = self._clean_json_response(output_text)

                ai_summary = json.loads(output_text)
            except Exception as e:
                self.logger.warning("AI summary generation failed", error=str(e))
                ai_summary = {"executive_summary": "Failed to generate AI summary"}
            
            # Compile statistics
            categories = {}
            types = {}
            jurisdictions = {}
            
            for article in articles:
                if article.ai_classification:
                    cat = article.ai_classification.get("category", "Unknown")
                    categories[cat] = categories.get(cat, 0) + 1
                    
                    typ = article.ai_classification.get("type", "Unknown")
                    types[typ] = types.get(typ, 0) + 1
                    
                    for jurisdiction in article.ai_classification.get("jurisdictions", []):
                        jurisdictions[jurisdiction] = jurisdictions.get(jurisdiction, 0) + 1
            
            summary_result = {
                "executive_summary": ai_summary.get("executive_summary", ""),
                "key_developments": ai_summary.get("key_developments", []),
                "regulatory_changes": ai_summary.get("regulatory_changes", []),
                "market_implications": ai_summary.get("market_implications", []),
                "important_dates": ai_summary.get("important_dates", []),
                "statistics": {
                    "total_articles": len(articles),
                    "processed_articles": len(article_summaries),
                    "categories": categories,
                    "types": types,
                    "jurisdictions": jurisdictions
                },
                "articles": article_summaries,
                "generated_at": datetime.now(timezone.utc).isoformat()
            }
            
            self.logger.info("Daily summary generated", total_articles=len(articles))
            return summary_result
            
        except Exception as e:
            self.log_error(e)
            raise
    
    def __del__(self):
        """Clean up database session."""
        if hasattr(self, 'db') and self.db:
            self.db.close()
