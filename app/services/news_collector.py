"""
News collection service for gathering carbon regulation news from various sources.
"""

import os
import re
from typing import List, Optional, Dict, Any
from datetime import datetime, timezone
from urllib.parse import urljoin, urlparse
from tavily import TavilyClient
from pydantic_ai import Agent
from pydantic_ai.models.openai import OpenAIChatModel
from pydantic_ai.providers.openrouter import OpenRouterProvider
from pydantic_ai.settings import ModelSettings
from pydantic import BaseModel
from sqlalchemy.orm import Session

from ..core.config import get_settings
from ..core.logging import LoggerMixin
from ..core.models import NewsArticle
from ..core.database import get_db_session


class ArticleURL(BaseModel):
    """Model for extracted article URLs"""
    urls: List[str]


class CollectedArticle(BaseModel):
    """Model for a collected news article before database storage"""
    title: str
    url: str
    content: str
    source_name: str
    published_date: Optional[datetime] = None


class NewsCollectionService(LoggerMixin):
    """Service for collecting news articles from various sources."""
    
    def __init__(self, db_session: Optional[Session] = None):
        """Initialize the news collection service."""
        self.settings = get_settings()
        self.db = db_session or get_db_session()
        self.tavily_client = self._init_tavily_client()
        self.url_extractor_agent = self._init_url_extractor_agent()
        
        self.log_method_call("__init__")
    
    def _init_tavily_client(self) -> TavilyClient:
        """Initialize Tavily client for web search and content extraction."""
        api_key = self.settings.tavily_api_key
        if not api_key:
            raise ValueError("TAVILY_API_KEY is required for news collection")
        
        self.logger.info("Initializing Tavily client")
        return TavilyClient(api_key)
    
    def _init_url_extractor_agent(self) -> Agent:
        """Initialize Pydantic AI agent for URL extraction from web pages."""
        api_key = self.settings.openrouter_api_key
        if not api_key:
            raise ValueError("OPENROUTER_API_KEY is required for AI-powered URL extraction")

        # Get model configuration for URL extraction
        model_config = self.settings.ai_processing.models.url_extractor

        model = OpenAIChatModel(
            model_config.model_name,
            provider=OpenRouterProvider(api_key=api_key),
            settings=ModelSettings(
                temperature=model_config.temperature,
                max_tokens=model_config.max_tokens,
            ),
        )
        
        system_prompt = self.settings.prompts.news_collector.url_extractor_system_prompt
        
        self.logger.info("Initializing URL extractor AI agent")
        return Agent(
            model,
            output_type=ArticleURL,
            system_prompt=system_prompt,
        )
    
    def collect_all_news(self) -> List[CollectedArticle]:
        """Collect news from all configured sources."""
        self.log_method_call("collect_all_news")
        articles = []
        
        # Collect from web search queries
        for query in self.settings.news_collector.search_queries:
            try:
                search_articles = self._collect_from_search_query(query)
                articles.extend(search_articles)
                self.logger.info(
                    "Collected articles from search query",
                    query=query,
                    count=len(search_articles)
                )
            except Exception as e:
                self.log_error(e, {"query": query})
        
        # Collect from specific sources
        for source_url in self.settings.news_collector.specific_sources:
            try:
                specific_articles = self._collect_from_specific_source(source_url)
                articles.extend(specific_articles)
                self.logger.info(
                    "Collected articles from specific source",
                    source_url=source_url,
                    count=len(specific_articles)
                )
            except Exception as e:
                self.log_error(e, {"source_url": source_url})
        
        self.logger.info("News collection completed", total_articles=len(articles))
        return articles
    
    def _collect_from_search_query(self, query: str) -> List[CollectedArticle]:
        """Collect articles from a web search query."""
        try:
            # Search for news using Tavily
            search_settings = self.settings.news_collector.search_settings
            search_response = self.tavily_client.search(
                query=query,
                topic="news",
                search_depth=search_settings.search_depth,
                time_range=self.settings.news_collector.default_time_range,
                max_results=self.settings.news_collector.max_articles_per_source,
                include_domains=search_settings.include_domains,
                exclude_domains=search_settings.exclude_domains
            )
            
            articles = []
            
            # Extract content from each search result
            for result in search_response.get('results', []):
                url = result.get('url')
                title = result.get('title', 'Unknown Title')
                content = result.get('content', '')
                
                if url and self._is_valid_article_url(url):
                    # Extract full content from the URL
                    try:
                        full_content = self._extract_full_content(url)
                        if full_content:
                            content = full_content
                    except Exception as e:
                        self.logger.warning("Failed to extract full content", url=url, error=str(e))
                    
                    article = CollectedArticle(
                        title=title,
                        url=url,
                        content=content,
                        source_name=self._extract_source_name(url)
                    )
                    articles.append(article)
            
            return articles
            
        except Exception as e:
            self.log_error(e, {"query": query})
            return []
    
    def _collect_from_specific_source(self, source_url: str) -> List[CollectedArticle]:
        """Collect articles from a specific source URL."""
        try:
            # Extract content from the main page
            extract_response = self.tavily_client.extract(urls=[source_url])
            
            if not extract_response or not extract_response.get('results'):
                return []
            
            page_content = extract_response['results'][0].get('content', '')
            
            # Use AI agent to extract article URLs
            article_urls = self._extract_article_urls_with_ai(page_content, source_url)
            
            # Extract content from each article URL
            articles = []
            max_articles = min(len(article_urls), self.settings.news_collector.max_articles_per_source)
            
            for url in article_urls[:max_articles]:
                try:
                    # Resolve relative URLs
                    full_url = urljoin(source_url, url) if not url.startswith('http') else url
                    
                    # Extract article content
                    content = self._extract_full_content(full_url)
                    if content:
                        title = self._extract_title_from_content(content) or f"Article from {self._extract_source_name(source_url)}"
                        
                        article = CollectedArticle(
                            title=title,
                            url=full_url,
                            content=content,
                            source_name=self._extract_source_name(source_url)
                        )
                        articles.append(article)
                
                except Exception as e:
                    self.logger.warning("Failed to extract article", url=url, error=str(e))
                    continue
            
            return articles
            
        except Exception as e:
            self.log_error(e, {"source_url": source_url})
            return []
    
    def _extract_article_urls_with_ai(self, page_content: str, base_url: str) -> List[str]:
        """Extract article URLs using AI agent."""
        prompt = self.settings.prompts.news_collector.url_extraction_prompt_template.format(
            base_url=base_url,
            page_content=page_content[:8000]  # Limit content to avoid token limits
        )
        
        try:
            result = self.url_extractor_agent.run_sync(prompt)
            return result.output.urls
        except Exception as e:
            self.logger.warning("AI URL extraction failed, using fallback", error=str(e))
            return self._extract_urls_fallback(page_content, base_url)
    
    def _extract_urls_fallback(self, content: str, base_url: str) -> List[str]:
        """Fallback method to extract URLs using regex."""
        url_patterns = [
            r'href=["\']([^"\']+/\d{4}/\d{2}/\d{2}/[^"\']+)["\']',  # Date-based URLs
            r'href=["\']([^"\']+/article/[^"\']+)["\']',             # Article paths
            r'href=["\']([^"\']+/news/[^"\']+)["\']',                # News paths
            r'href=["\']([^"\']+/sustainability/[^"\']+)["\']',      # Sustainability paths
        ]
        
        urls = []
        for pattern in url_patterns:
            matches = re.findall(pattern, content)
            for match in matches:
                if not match.startswith('http'):
                    match = urljoin(base_url, match)
                urls.append(match)
        
        # Remove duplicates and return first 10
        return list(dict.fromkeys(urls))[:10]
    
    def _extract_full_content(self, url: str) -> Optional[str]:
        """Extract full content from a URL using Tavily."""
        try:
            extract_response = self.tavily_client.extract(urls=[url])
            if extract_response and extract_response.get('results'):
                return extract_response['results'][0].get('content', '')
        except Exception as e:
            self.logger.warning("Content extraction failed", url=url, error=str(e))
        return None
    
    def _extract_title_from_content(self, content: str) -> Optional[str]:
        """Extract title from article content."""
        lines = content.split('\n')
        for line in lines[:10]:  # Check first 10 lines
            line = line.strip()
            if line and len(line) > 10 and len(line) < 200:
                # Skip common non-title patterns
                if not any(skip in line.lower() for skip in ['subscribe', 'menu', 'search', 'login', 'advertisement']):
                    return line
        return None
    
    def _extract_source_name(self, url: str) -> str:
        """Extract source name from URL."""
        try:
            parsed = urlparse(url)
            domain = parsed.netloc.lower()
            # Remove www. prefix
            if domain.startswith('www.'):
                domain = domain[4:]
            return domain.split('.')[0].title()
        except:
            return "Unknown Source"
    
    def _is_valid_article_url(self, url: str) -> bool:
        """Check if URL looks like a valid article URL."""
        # Basic validation - could be enhanced
        return (
            url.startswith(('http://', 'https://')) and
            not any(skip in url.lower() for skip in ['javascript:', 'mailto:', '#', '?'])
        )
    
    def save_articles_to_database(self, articles: List[CollectedArticle]) -> List[NewsArticle]:
        """Save collected articles to the database."""
        self.log_method_call("save_articles_to_database", count=len(articles))
        
        saved_articles = []
        
        for article in articles:
            try:
                # Check if article already exists
                existing = self.db.query(NewsArticle).filter(NewsArticle.url == article.url).first()
                if existing:
                    self.logger.debug("Article already exists", url=article.url)
                    continue
                
                # Create new article
                db_article = NewsArticle(
                    title=article.title,
                    url=article.url,
                    content=article.content,
                    source_name=article.source_name,
                    published_date=article.published_date,
                    collected_at=datetime.now(timezone.utc)
                )
                
                self.db.add(db_article)
                self.db.commit()
                self.db.refresh(db_article)
                
                saved_articles.append(db_article)
                self.logger.debug("Article saved", id=db_article.id, title=article.title[:50])
                
            except Exception as e:
                self.db.rollback()
                self.log_error(e, {"article_url": article.url})
        
        self.logger.info("Articles saved to database", saved_count=len(saved_articles))
        return saved_articles
    
    def collect_and_save_news(self) -> Dict[str, Any]:
        """Collect news and save to database. Returns summary statistics."""
        self.log_method_call("collect_and_save_news")
        
        try:
            # Collect articles
            collected_articles = self.collect_all_news()
            
            # Save to database
            saved_articles = self.save_articles_to_database(collected_articles)
            
            summary = {
                "collected_count": len(collected_articles),
                "saved_count": len(saved_articles),
                "duplicate_count": len(collected_articles) - len(saved_articles),
                "sources": list(set(article.source_name for article in collected_articles)),
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            
            self.logger.info("News collection and save completed", **summary)
            return summary
            
        except Exception as e:
            self.log_error(e)
            raise
    
    def __del__(self):
        """Clean up database session."""
        if hasattr(self, 'db') and self.db:
            self.db.close()
