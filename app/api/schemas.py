"""
Pydantic schemas for API request and response models.
"""

from datetime import datetime, timezone
from typing import Optional, List, Dict, Any, Union, Annotated
from pydantic import BaseModel, Field, ConfigDict, field_serializer
from enum import Enum


class TaskStatusEnum(str, Enum):
    """Task execution status enumeration."""
    PENDING = "pending"
    RUNNING = "running"
    SUCCESS = "success"
    FAILED = "failed"


class TaskTypeEnum(str, Enum):
    """Task type enumeration."""
    NEWS_COLLECTION = "news_collection"
    AI_PROCESSING = "ai_processing"
    DAILY_SUMMARY = "daily_summary"
    FULL_PIPELINE = "full_pipeline"


class NotificationTypeEnum(str, Enum):
    """Notification type enumeration."""
    WEBHOOK = "webhook"
    SLACK = "slack"
    EMAIL = "email"


# Base model with datetime serialization
class BaseModelWithDatetime(BaseModel):
    """Base model with datetime field serialization."""
    model_config = ConfigDict()

    @field_serializer('*', when_used='json')
    def serialize_datetime_fields(self, value: Any) -> Any:
        """Serialize datetime fields to ISO format."""
        if isinstance(value, datetime):
            return value.isoformat()
        return value


# Base schemas
class BaseResponse(BaseModel):
    """Base response model."""
    model_config = ConfigDict()

    success: bool = Field(..., description="Whether the request was successful")
    message: str = Field(..., description="Response message")
    timestamp: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), description="Response timestamp")

    @field_serializer('timestamp')
    def serialize_timestamp(self, value: datetime) -> str:
        """Serialize datetime to ISO format."""
        return value.isoformat()


class ErrorResponse(BaseResponse):
    """Error response model."""
    success: bool = Field(default=False, description="Always false for error responses")
    error_type: Optional[str] = Field(None, description="Type of error")
    error_details: Optional[Dict[str, Any]] = Field(None, description="Additional error details")


# News Article schemas
class NewsArticleBase(BaseModelWithDatetime):
    """Base news article schema."""

    title: str = Field(..., description="Article title")
    url: str = Field(..., description="Article URL")
    source_name: str = Field(..., description="Source name")
    published_date: Optional[datetime] = Field(None, description="Published date")
    collected_at: datetime = Field(..., description="Collection timestamp")


class NewsArticleResponse(NewsArticleBase):
    """News article response schema."""
    model_config = ConfigDict(from_attributes=True)

    id: int = Field(..., description="Article ID")
    is_processed: bool = Field(..., description="Whether article has been processed by AI")
    processed_at: Optional[datetime] = Field(None, description="AI processing timestamp")
    ai_classification: Optional[Dict[str, Any]] = Field(None, description="AI classification results")
    ai_summary: Optional[str] = Field(None, description="AI-generated summary")
    ai_key_points: Optional[List[str]] = Field(None, description="AI-extracted key points")


class NewsArticleListResponse(BaseResponse):
    """News article list response schema."""
    articles: List[NewsArticleResponse] = Field(..., description="List of news articles")
    total_count: int = Field(..., description="Total number of articles")
    page: int = Field(..., description="Current page number")
    page_size: int = Field(..., description="Number of articles per page")


# Task Execution schemas
class TaskExecutionBase(BaseModelWithDatetime):
    """Base task execution schema."""

    task_name: str = Field(..., description="Task name")
    task_type: TaskTypeEnum = Field(..., description="Task type")
    status: TaskStatusEnum = Field(..., description="Task status")
    started_at: datetime = Field(..., description="Task start timestamp")


class TaskExecutionResponse(TaskExecutionBase):
    """Task execution response schema."""
    model_config = ConfigDict(from_attributes=True)

    id: int = Field(..., description="Task execution ID")
    completed_at: Optional[datetime] = Field(None, description="Task completion timestamp")
    duration_seconds: Optional[float] = Field(None, description="Task duration in seconds")
    result_summary: Optional[Dict[str, Any]] = Field(None, description="Task result summary")
    error_message: Optional[str] = Field(None, description="Error message if task failed")
    error_details: Optional[Dict[str, Any]] = Field(None, description="Additional error details")


class TaskExecutionListResponse(BaseResponse):
    """Task execution list response schema."""
    executions: List[TaskExecutionResponse] = Field(..., description="List of task executions")
    total_count: int = Field(..., description="Total number of executions")
    page: int = Field(..., description="Current page number")
    page_size: int = Field(..., description="Number of executions per page")


# Task Result schemas
class TaskResultBase(BaseModelWithDatetime):
    """Base task result schema."""

    result_type: str = Field(..., description="Result type")
    title: str = Field(..., description="Result title")
    summary: str = Field(..., description="Result summary")
    created_at: datetime = Field(..., description="Result creation timestamp")


class TaskResultResponse(TaskResultBase):
    """Task result response schema."""
    model_config = ConfigDict(from_attributes=True)

    id: int = Field(..., description="Task result ID")
    task_execution_id: int = Field(..., description="Associated task execution ID")
    key_findings: Optional[List[str]] = Field(None, description="Key findings")
    statistics: Optional[Dict[str, Any]] = Field(None, description="Result statistics")
    result_metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")


class TaskResultListResponse(BaseResponse):
    """Task result list response schema."""
    results: List[TaskResultResponse] = Field(..., description="List of task results")
    total_count: int = Field(..., description="Total number of results")
    page: int = Field(..., description="Current page number")
    page_size: int = Field(..., description="Number of results per page")


# Manual Task Trigger schemas
class ManualTaskTriggerRequest(BaseModel):
    """Manual task trigger request schema."""
    task_type: TaskTypeEnum = Field(..., description="Type of task to trigger")
    task_name: Optional[str] = Field(None, description="Optional custom task name")


class ManualTaskTriggerResponse(BaseResponse):
    """Manual task trigger response schema."""
    task_execution: TaskExecutionResponse = Field(..., description="Created task execution")


# Statistics schemas
class TaskStatistics(BaseModelWithDatetime):
    """Task execution statistics schema."""

    total_executions: int = Field(..., description="Total number of task executions")
    successful_executions: int = Field(..., description="Number of successful executions")
    failed_executions: int = Field(..., description="Number of failed executions")
    success_rate: float = Field(..., description="Success rate (0.0 to 1.0)")
    last_execution: Optional[datetime] = Field(None, description="Timestamp of last execution")
    last_success: Optional[datetime] = Field(None, description="Timestamp of last successful execution")
    scheduler_running: bool = Field(..., description="Whether scheduler is currently running")


class NewsStatistics(BaseModel):
    """News collection statistics schema."""
    total_articles: int = Field(..., description="Total number of articles")
    processed_articles: int = Field(..., description="Number of processed articles")
    unprocessed_articles: int = Field(..., description="Number of unprocessed articles")
    articles_today: int = Field(..., description="Articles collected today")
    sources_count: int = Field(..., description="Number of unique sources")
    categories: Dict[str, int] = Field(default_factory=dict, description="Articles by category")
    types: Dict[str, int] = Field(default_factory=dict, description="Articles by type")


class SystemStatistics(BaseModelWithDatetime):
    """System-wide statistics schema."""

    task_stats: TaskStatistics = Field(..., description="Task execution statistics")
    news_stats: NewsStatistics = Field(..., description="News collection statistics")
    database_health: bool = Field(..., description="Database health status")
    last_updated: datetime = Field(..., description="Statistics last updated timestamp")


class StatisticsResponse(BaseResponse):
    """Statistics response schema."""
    statistics: SystemStatistics = Field(..., description="System statistics")


# Health Check schemas
class HealthCheckResponse(BaseResponse):
    """Health check response schema."""

    status: str = Field(..., description="System health status")
    database_connected: bool = Field(..., description="Database connection status")
    scheduler_running: bool = Field(..., description="Scheduler status")
    total_articles: int = Field(..., description="Total articles in database")
    last_task_execution: Optional[datetime] = Field(None, description="Last task execution timestamp")
    uptime_seconds: Optional[float] = Field(None, description="Application uptime in seconds")


# Notification schemas
class NotificationLogResponse(BaseModelWithDatetime):
    """Notification log response schema."""
    model_config = ConfigDict(from_attributes=True)

    id: int = Field(..., description="Notification log ID")
    notification_type: NotificationTypeEnum = Field(..., description="Notification type")
    recipient: str = Field(..., description="Notification recipient")
    subject: str = Field(..., description="Notification subject")
    status: str = Field(..., description="Notification status")
    sent_at: datetime = Field(..., description="Sent timestamp")
    response_code: Optional[int] = Field(None, description="HTTP response code")
    retry_count: int = Field(..., description="Number of retry attempts")


class NotificationListResponse(BaseResponse):
    """Notification list response schema."""
    notifications: List[NotificationLogResponse] = Field(..., description="List of notifications")
    total_count: int = Field(..., description="Total number of notifications")
    page: int = Field(..., description="Current page number")
    page_size: int = Field(..., description="Number of notifications per page")


# Daily Summary schemas
class DailySummaryResponse(BaseModelWithDatetime):
    """Daily summary response schema."""

    date: str = Field(..., description="Summary date (YYYY-MM-DD)")
    executive_summary: str = Field(..., description="Executive summary")
    key_developments: List[str] = Field(default_factory=list, description="Key developments")
    regulatory_changes: List[str] = Field(default_factory=list, description="Regulatory changes")
    market_implications: List[str] = Field(default_factory=list, description="Market implications")
    important_dates: List[str] = Field(default_factory=list, description="Important upcoming dates")
    statistics: Dict[str, Any] = Field(default_factory=dict, description="Summary statistics")
    articles_count: int = Field(..., description="Number of articles in summary")
    generated_at: datetime = Field(..., description="Summary generation timestamp")


class DailySummaryListResponse(BaseResponse):
    """Daily summary list response schema."""
    summaries: List[DailySummaryResponse] = Field(..., description="List of daily summaries")
    total_count: int = Field(..., description="Total number of summaries")
    page: int = Field(..., description="Current page number")
    page_size: int = Field(..., description="Number of summaries per page")
