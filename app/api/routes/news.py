"""
News articles and daily summaries API routes.
"""

from datetime import datetime, timezone, date
from typing import Optional, List
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from sqlalchemy import desc, and_

from ...core.database import get_db
from ...core.models import NewsArticle, TaskResult
from ...core.logging import get_logger
from ..schemas import (
    NewsArticleResponse, NewsArticleListResponse, DailySummaryResponse, 
    DailySummaryListResponse, BaseResponse
)

logger = get_logger(__name__)
router = APIRouter(prefix="/news", tags=["News"])


@router.get("/articles", response_model=NewsArticleListResponse)
async def get_news_articles(
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(50, ge=1, le=100, description="Number of items per page"),
    processed_only: Optional[bool] = Query(None, description="Filter by processing status"),
    source: Optional[str] = Query(None, description="Filter by source name"),
    category: Optional[str] = Query(None, description="Filter by AI classification category"),
    date_from: Optional[date] = Query(None, description="Filter articles from this date"),
    date_to: Optional[date] = Query(None, description="Filter articles to this date"),
    db: Session = Depends(get_db)
):
    """
    Get a paginated list of news articles with optional filtering.
    
    - **page**: Page number (starts from 1)
    - **page_size**: Number of items per page (max 100)
    - **processed_only**: Filter by AI processing status (true/false)
    - **source**: Filter by source name
    - **category**: Filter by AI classification category
    - **date_from**: Filter articles collected from this date (YYYY-MM-DD)
    - **date_to**: Filter articles collected to this date (YYYY-MM-DD)
    """
    try:
        # Build query
        query = db.query(NewsArticle)
        
        # Apply filters
        if processed_only is not None:
            query = query.filter(NewsArticle.is_processed == processed_only)
        
        if source:
            query = query.filter(NewsArticle.source_name.ilike(f"%{source}%"))
        
        if category:
            # Filter by AI classification category (JSON field)
            query = query.filter(
                NewsArticle.ai_classification.op('->>')('category').ilike(f"%{category}%")
            )
        
        if date_from:
            query = query.filter(NewsArticle.collected_at >= date_from)
        
        if date_to:
            # Add one day to include the entire day
            date_to_end = datetime.combine(date_to, datetime.max.time())
            query = query.filter(NewsArticle.collected_at <= date_to_end)
        
        # Get total count
        total_count = query.count()
        
        # Apply pagination and ordering
        articles = query.order_by(desc(NewsArticle.collected_at)).offset(
            (page - 1) * page_size
        ).limit(page_size).all()
        
        # Convert to response models
        article_responses = [
            NewsArticleResponse.model_validate(article) 
            for article in articles
        ]
        
        return NewsArticleListResponse(
            success=True,
            message=f"Retrieved {len(article_responses)} news articles",
            articles=article_responses,
            total_count=total_count,
            page=page,
            page_size=page_size
        )
        
    except Exception as e:
        logger.error("Failed to get news articles", error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get news articles: {str(e)}"
        )


@router.get("/articles/{article_id}", response_model=NewsArticleResponse)
async def get_news_article(article_id: int, db: Session = Depends(get_db)):
    """
    Get details of a specific news article by ID.
    """
    try:
        article = db.query(NewsArticle).filter(NewsArticle.id == article_id).first()
        
        if not article:
            raise HTTPException(
                status_code=404,
                detail=f"News article with ID {article_id} not found"
            )
        
        return NewsArticleResponse.model_validate(article)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get news article", article_id=article_id, error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get news article: {str(e)}"
        )


@router.get("/articles/{article_id}/content")
async def get_article_content(article_id: int, db: Session = Depends(get_db)):
    """
    Get the full content of a specific news article.
    """
    try:
        article = db.query(NewsArticle).filter(NewsArticle.id == article_id).first()
        
        if not article:
            raise HTTPException(
                status_code=404,
                detail=f"News article with ID {article_id} not found"
            )
        
        return {
            "success": True,
            "message": "Article content retrieved",
            "article_id": article.id,
            "title": article.title,
            "url": article.url,
            "source_name": article.source_name,
            "content": article.content,
            "collected_at": article.collected_at,
            "is_processed": article.is_processed
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get article content", article_id=article_id, error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get article content: {str(e)}"
        )


@router.get("/summaries", response_model=DailySummaryListResponse)
async def get_daily_summaries(
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(20, ge=1, le=50, description="Number of items per page"),
    date_from: Optional[date] = Query(None, description="Filter summaries from this date"),
    date_to: Optional[date] = Query(None, description="Filter summaries to this date"),
    db: Session = Depends(get_db)
):
    """
    Get a paginated list of daily summaries.
    
    - **page**: Page number (starts from 1)
    - **page_size**: Number of items per page (max 50)
    - **date_from**: Filter summaries from this date (YYYY-MM-DD)
    - **date_to**: Filter summaries to this date (YYYY-MM-DD)
    """
    try:
        # Build query for daily summary results
        query = db.query(TaskResult).filter(TaskResult.result_type == "daily_summary")
        
        # Apply date filters
        if date_from:
            query = query.filter(TaskResult.created_at >= date_from)
        
        if date_to:
            # Add one day to include the entire day
            date_to_end = datetime.combine(date_to, datetime.max.time())
            query = query.filter(TaskResult.created_at <= date_to_end)
        
        # Get total count
        total_count = query.count()
        
        # Apply pagination and ordering
        summary_results = query.order_by(desc(TaskResult.created_at)).offset(
            (page - 1) * page_size
        ).limit(page_size).all()
        
        # Convert to response models
        summary_responses = []
        for result in summary_results:
            metadata = result.result_metadata or {}
            
            summary_response = DailySummaryResponse(
                date=result.created_at.date().isoformat(),
                executive_summary=metadata.get("executive_summary", result.summary),
                key_developments=metadata.get("key_developments", result.key_findings or []),
                regulatory_changes=metadata.get("regulatory_changes", []),
                market_implications=metadata.get("market_implications", []),
                important_dates=metadata.get("important_dates", []),
                statistics=metadata.get("statistics", result.statistics or {}),
                articles_count=metadata.get("statistics", {}).get("total_articles", 0),
                generated_at=result.created_at
            )
            summary_responses.append(summary_response)
        
        return DailySummaryListResponse(
            success=True,
            message=f"Retrieved {len(summary_responses)} daily summaries",
            summaries=summary_responses,
            total_count=total_count,
            page=page,
            page_size=page_size
        )
        
    except Exception as e:
        logger.error("Failed to get daily summaries", error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get daily summaries: {str(e)}"
        )


@router.get("/summaries/latest", response_model=DailySummaryResponse)
async def get_latest_daily_summary(db: Session = Depends(get_db)):
    """
    Get the most recent daily summary.
    """
    try:
        latest_summary = db.query(TaskResult).filter(
            TaskResult.result_type == "daily_summary"
        ).order_by(desc(TaskResult.created_at)).first()
        
        if not latest_summary:
            raise HTTPException(
                status_code=404,
                detail="No daily summaries found"
            )
        
        metadata = latest_summary.result_metadata or {}
        
        return DailySummaryResponse(
            date=latest_summary.created_at.date().isoformat(),
            executive_summary=metadata.get("executive_summary", latest_summary.summary),
            key_developments=metadata.get("key_developments", latest_summary.key_findings or []),
            regulatory_changes=metadata.get("regulatory_changes", []),
            market_implications=metadata.get("market_implications", []),
            important_dates=metadata.get("important_dates", []),
            statistics=metadata.get("statistics", latest_summary.statistics or {}),
            articles_count=metadata.get("statistics", {}).get("total_articles", 0),
            generated_at=latest_summary.created_at
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get latest daily summary", error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get latest daily summary: {str(e)}"
        )


@router.get("/summaries/{summary_date}")
async def get_daily_summary_by_date(summary_date: date, db: Session = Depends(get_db)):
    """
    Get daily summary for a specific date (YYYY-MM-DD).
    """
    try:
        # Find summary for the specified date
        start_of_day = datetime.combine(summary_date, datetime.min.time())
        end_of_day = datetime.combine(summary_date, datetime.max.time())
        
        summary = db.query(TaskResult).filter(
            TaskResult.result_type == "daily_summary",
            and_(
                TaskResult.created_at >= start_of_day,
                TaskResult.created_at <= end_of_day
            )
        ).order_by(desc(TaskResult.created_at)).first()
        
        if not summary:
            raise HTTPException(
                status_code=404,
                detail=f"No daily summary found for date {summary_date}"
            )
        
        metadata = summary.result_metadata or {}
        
        return DailySummaryResponse(
            date=summary_date.isoformat(),
            executive_summary=metadata.get("executive_summary", summary.summary),
            key_developments=metadata.get("key_developments", summary.key_findings or []),
            regulatory_changes=metadata.get("regulatory_changes", []),
            market_implications=metadata.get("market_implications", []),
            important_dates=metadata.get("important_dates", []),
            statistics=metadata.get("statistics", summary.statistics or {}),
            articles_count=metadata.get("statistics", {}).get("total_articles", 0),
            generated_at=summary.created_at
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get daily summary by date", date=summary_date, error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get daily summary: {str(e)}"
        )


@router.get("/sources")
async def get_news_sources(db: Session = Depends(get_db)):
    """
    Get list of all news sources with article counts.
    """
    try:
        # Get source statistics
        from sqlalchemy import func
        
        source_stats = db.query(
            NewsArticle.source_name,
            func.count(NewsArticle.id).label('total_articles'),
            func.count(NewsArticle.id).filter(NewsArticle.is_processed == True).label('processed_articles'),
            func.max(NewsArticle.collected_at).label('last_collected')
        ).group_by(NewsArticle.source_name).all()
        
        sources = []
        for stat in source_stats:
            sources.append({
                "source_name": stat.source_name,
                "total_articles": stat.total_articles,
                "processed_articles": stat.processed_articles,
                "unprocessed_articles": stat.total_articles - stat.processed_articles,
                "last_collected": stat.last_collected
            })
        
        # Sort by total articles descending
        sources.sort(key=lambda x: x['total_articles'], reverse=True)
        
        return {
            "success": True,
            "message": f"Retrieved {len(sources)} news sources",
            "sources": sources,
            "total_sources": len(sources)
        }
        
    except Exception as e:
        logger.error("Failed to get news sources", error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get news sources: {str(e)}"
        )


@router.get("/categories")
async def get_article_categories(db: Session = Depends(get_db)):
    """
    Get list of all article categories from AI classification with counts.
    """
    try:
        # Get processed articles with classifications
        processed_articles = db.query(NewsArticle).filter(
            NewsArticle.is_processed == True,
            NewsArticle.ai_classification.isnot(None)
        ).all()
        
        categories = {}
        types = {}
        jurisdictions = {}
        
        for article in processed_articles:
            if article.ai_classification:
                # Count categories
                category = article.ai_classification.get("category", "Unknown")
                categories[category] = categories.get(category, 0) + 1
                
                # Count types
                article_type = article.ai_classification.get("type", "Unknown")
                types[article_type] = types.get(article_type, 0) + 1
                
                # Count jurisdictions
                for jurisdiction in article.ai_classification.get("jurisdictions", []):
                    jurisdictions[jurisdiction] = jurisdictions.get(jurisdiction, 0) + 1
        
        return {
            "success": True,
            "message": "Retrieved article classification statistics",
            "categories": dict(sorted(categories.items(), key=lambda x: x[1], reverse=True)),
            "types": dict(sorted(types.items(), key=lambda x: x[1], reverse=True)),
            "jurisdictions": dict(sorted(jurisdictions.items(), key=lambda x: x[1], reverse=True)),
            "total_processed_articles": len(processed_articles)
        }
        
    except Exception as e:
        logger.error("Failed to get article categories", error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get article categories: {str(e)}"
        )
