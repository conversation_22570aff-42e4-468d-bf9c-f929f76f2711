"""
Main FastAPI application for the Carbon Regulation News API.
"""

import time
from contextlib import asynccontextmanager
from fastapi import FastAP<PERSON>, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.openapi.docs import get_swagger_ui_html
from fastapi.openapi.utils import get_openapi

from ..core.config import get_settings
from ..core.logging import setup_logging, get_logger
from ..core.database import DatabaseManager
from .routes import health, tasks, news
from .schemas import ErrorResponse

# Initialize logging
setup_logging()
logger = get_logger(__name__)

# Track application start time
app_start_time = time.time()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    # Startup
    logger.info("Starting Carbon Regulation News API")
    
    try:
        # Initialize database
        DatabaseManager.initialize()
        logger.info("Database initialized successfully")
        
        # TODO: Initialize and start scheduler service
        # scheduler_service = TaskSchedulerService()
        # scheduler_service.start_scheduler()
        # logger.info("Task scheduler started")
        
        yield
        
    except Exception as e:
        logger.error("Failed to initialize application", error=str(e))
        raise
    
    # Shutdown
    logger.info("Shutting down Carbon Regulation News API")
    
    try:
        # TODO: Stop scheduler service
        # scheduler_service.stop_scheduler()
        # logger.info("Task scheduler stopped")
        
        logger.info("Application shutdown complete")
        
    except Exception as e:
        logger.error("Error during application shutdown", error=str(e))


# Create FastAPI application
def create_app() -> FastAPI:
    """Create and configure the FastAPI application."""
    settings = get_settings()
    
    app = FastAPI(
        title="Carbon Regulation News API",
        description="""
        A comprehensive API for collecting, analyzing, and summarizing carbon regulation and climate policy news.
        
        ## Features
        
        * **Automated News Collection**: Collect news from multiple sources using web search and specific URLs
        * **AI-Powered Analysis**: Process articles with advanced AI to extract structured information
        * **Daily Summaries**: Generate comprehensive daily summaries for climate experts
        * **Task Management**: Monitor and manually trigger collection and processing tasks
        * **Notification System**: Extensible notification system with webhook and Slack support
        
        ## API Sections
        
        * **Health**: System health checks and statistics
        * **Tasks**: Task execution monitoring and manual triggering
        * **News**: News articles, daily summaries, and content management
        
        ## Authentication
        
        This API currently does not require authentication as specified in the requirements.
        """,
        version="1.0.0",
        contact={
            "name": "Carbon Regulation News Team",
            "email": "<EMAIL>",
        },
        license_info={
            "name": "MIT License",
            "url": "https://opensource.org/licenses/MIT",
        },
        lifespan=lifespan,
        debug=settings.debug
    )
    
    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # Configure appropriately for production
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # Include routers
    app.include_router(health.router)
    app.include_router(tasks.router)
    app.include_router(news.router)
    
    return app


# Create the application instance
app = create_app()


# Global exception handler
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """Global exception handler for unhandled errors."""
    logger.error(
        "Unhandled exception",
        path=request.url.path,
        method=request.method,
        error=str(exc),
        error_type=type(exc).__name__
    )
    
    return JSONResponse(
        status_code=500,
        content=ErrorResponse(
            message="An internal server error occurred",
            error_type=type(exc).__name__,
            error_details={"path": str(request.url.path), "method": request.method}
        ).model_dump()
    )


# HTTP exception handler
@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """Handler for HTTP exceptions."""
    logger.warning(
        "HTTP exception",
        path=request.url.path,
        method=request.method,
        status_code=exc.status_code,
        detail=exc.detail
    )
    
    error_response = ErrorResponse(
        message=exc.detail,
        error_type="HTTPException",
        error_details={"status_code": exc.status_code}
    )
    return JSONResponse(
        status_code=exc.status_code,
        content=error_response.model_dump(mode='json')
    )


# Root endpoint
@app.get("/")
async def root():
    """Root endpoint with API information."""
    settings = get_settings()
    uptime_seconds = time.time() - app_start_time
    
    return {
        "message": "Carbon Regulation News API",
        "version": "1.0.0",
        "description": "API for collecting and analyzing carbon regulation news",
        "environment": settings.environment,
        "uptime_seconds": uptime_seconds,
        "docs_url": "/docs",
        "redoc_url": "/redoc",
        "health_check": "/health",
        "endpoints": {
            "health": "/health",
            "tasks": "/tasks",
            "news": "/news"
        }
    }


# Custom OpenAPI schema
def custom_openapi():
    """Generate custom OpenAPI schema."""
    if app.openapi_schema:
        return app.openapi_schema
    
    openapi_schema = get_openapi(
        title="Carbon Regulation News API",
        version="1.0.0",
        description=app.description,
        routes=app.routes,
    )
    
    # Add custom schema extensions
    openapi_schema["info"]["x-logo"] = {
        "url": "https://example.com/logo.png"
    }
    
    # Add server information
    openapi_schema["servers"] = [
        {
            "url": "/",
            "description": "Current server"
        }
    ]
    
    # Add tags
    openapi_schema["tags"] = [
        {
            "name": "Health",
            "description": "System health checks and monitoring"
        },
        {
            "name": "Tasks",
            "description": "Task execution and management"
        },
        {
            "name": "News",
            "description": "News articles and daily summaries"
        }
    ]
    
    app.openapi_schema = openapi_schema
    return app.openapi_schema


app.openapi = custom_openapi


# Middleware for request logging
@app.middleware("http")
async def log_requests(request: Request, call_next):
    """Log all HTTP requests."""
    start_time = time.time()
    
    # Log request
    logger.info(
        "HTTP request started",
        method=request.method,
        path=request.url.path,
        query_params=str(request.query_params),
        client_ip=request.client.host if request.client else None
    )
    
    # Process request
    response = await call_next(request)
    
    # Calculate duration
    duration = time.time() - start_time
    
    # Log response
    logger.info(
        "HTTP request completed",
        method=request.method,
        path=request.url.path,
        status_code=response.status_code,
        duration_seconds=round(duration, 3)
    )
    
    return response


# Health check for load balancers
@app.get("/ping")
async def ping():
    """Simple ping endpoint for load balancer health checks."""
    return {"status": "ok", "timestamp": time.time()}


if __name__ == "__main__":
    import uvicorn
    
    settings = get_settings()
    
    logger.info(
        "Starting application server",
        host=settings.api.host,
        port=settings.api.port,
        debug=settings.api.debug
    )
    
    uvicorn.run(
        "app.api.main:app",
        host=settings.api.host,
        port=settings.api.port,
        reload=settings.api.debug,
        log_level=settings.log_level.lower()
    )
