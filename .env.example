# Carbon Regulation News Application Configuration
# Copy this file to .env and fill in your actual values

# =============================================================================
# API KEYS (REQUIRED)
# =============================================================================

# Tavily API Key - Required for web search and content extraction
# Get your key at: https://tavily.com
TAVILY_API_KEY=your_tavily_api_key_here

# OpenRouter API Key - Required for AI processing
# Get your key at: https://openrouter.ai
OPENROUTER_API_KEY=your_openrouter_api_key_here

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================

# Environment: development, production, test
ENVIRONMENT=development

# Debug mode: true/false
DEBUG=true

# Logging level: DEBUG, INFO, WARNING, ERROR
LOG_LEVEL=INFO

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# Database URL - SQLite by default
# For production, consider PostgreSQL: postgresql://user:password@localhost/dbname
DATABASE__URL=sqlite:///./carbon_news.db

# Enable SQL query logging: true/false
DATABASE__ECHO=false

# =============================================================================
# API SERVER CONFIGURATION
# =============================================================================

# API server host
API__HOST=0.0.0.0

# API server port
API__PORT=8000

# API debug mode: true/false
API__DEBUG=true

# =============================================================================
# NEWS COLLECTION CONFIGURATION
# =============================================================================

# Maximum articles to collect per source
NEWS_COLLECTOR__MAX_ARTICLES_PER_SOURCE=10

# Default time range for news search: hour, day, week, month
NEWS_COLLECTOR__DEFAULT_TIME_RANGE=day

# Search queries for carbon regulation news (JSON array format)
# NEWS_COLLECTOR__SEARCH_QUERIES=["carbon regulations emission standards", "climate policy sustainability"]

# Specific news sources to monitor (JSON array format)
# NEWS_COLLECTOR__SPECIFIC_SOURCES=["https://www.reuters.com/sustainability/", "https://www.bloomberg.com/green"]

# =============================================================================
# TASK SCHEDULER CONFIGURATION
# =============================================================================

# Daily task run time (HH:MM format, 24-hour)
SCHEDULER__DAILY_RUN_TIME=09:00

# Maximum number of task execution records to keep
SCHEDULER__MAX_TASK_HISTORY=100

# Task execution timeout in minutes
SCHEDULER__TASK_TIMEOUT_MINUTES=30

# =============================================================================
# NOTIFICATION CONFIGURATION
# =============================================================================

# Enable/disable notifications: true/false
NOTIFICATIONS__ENABLE_NOTIFICATIONS=true

# Generic webhook URL for notifications
# NOTIFICATIONS__WEBHOOK_URL=https://your-webhook-endpoint.com/notifications

# Slack webhook URL for Slack notifications
# Get this from your Slack workspace: Slack App > Incoming Webhooks
# NOTIFICATIONS__SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK

# =============================================================================
# DEVELOPMENT/TESTING CONFIGURATION
# =============================================================================

# Test database URL (used during testing)
# TEST_DATABASE__URL=sqlite:///:memory:

# Mock API responses during development: true/false
# MOCK_API_RESPONSES=false

# =============================================================================
# PRODUCTION CONFIGURATION
# =============================================================================

# For production deployment, consider these additional settings:

# Use a more robust database
# DATABASE__URL=postgresql://username:password@localhost:5432/carbon_news

# Disable debug mode
# DEBUG=false
# API__DEBUG=false

# Set appropriate log level
# LOG_LEVEL=WARNING

# Configure proper notification endpoints
# NOTIFICATIONS__WEBHOOK_URL=https://your-production-webhook.com
# NOTIFICATIONS__SLACK_WEBHOOK_URL=https://hooks.slack.com/services/...

# =============================================================================
# OPTIONAL: ADVANCED CONFIGURATION
# =============================================================================

# Custom user agent for web requests
# USER_AGENT=CarbonRegulationNews/1.0

# Request timeout in seconds
# REQUEST_TIMEOUT=30

# Maximum retry attempts for failed requests
# MAX_RETRIES=3

# Rate limiting: requests per minute
# RATE_LIMIT_PER_MINUTE=60

# =============================================================================
# SECURITY NOTES
# =============================================================================

# 1. Never commit the actual .env file to version control
# 2. Keep your API keys secure and rotate them regularly
# 3. Use environment-specific configurations
# 4. Consider using a secrets management service in production
# 5. Restrict API access with firewalls and authentication in production

# =============================================================================
# EXAMPLE CONFIGURATIONS
# =============================================================================

# Development Example:
# ENVIRONMENT=development
# DEBUG=true
# LOG_LEVEL=DEBUG
# DATABASE__URL=sqlite:///./dev_carbon_news.db

# Production Example:
# ENVIRONMENT=production
# DEBUG=false
# LOG_LEVEL=INFO
# DATABASE__URL=postgresql://carbon_user:<EMAIL>:5432/carbon_news
# NOTIFICATIONS__WEBHOOK_URL=https://api.company.com/webhooks/carbon-news
# NOTIFICATIONS__SLACK_WEBHOOK_URL=*****************************************************************************
